/**
 * Supabase JWT utilities and types
 */

import jwt from 'jsonwebtoken';
import { getSupabaseClient } from '../lib/supabase';
import { logger } from './logger';

export interface SupabaseUser {
  id: string;
  email: string;
  name?: string;
  role?: string;
  app_metadata?: Record<string, unknown>;
  user_metadata?: Record<string, unknown>;
}

export interface SupabaseJWTPayload {
  sub: string; // User ID
  email: string;
  role?: string;
  app_metadata?: Record<string, unknown>;
  user_metadata?: Record<string, unknown>;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
}

/**
 * Extract user information from Supabase JWT token
 */
export function extractUserFromToken(token: string): SupabaseUser | null {
  try {
    // Decode without verification (verification should be done separately)
    const decoded = jwt.decode(token) as SupabaseJWTPayload;
    
    if (!decoded || !decoded.sub || !decoded.email) {
      return null;
    }
    
    return {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.role,
      app_metadata: decoded.app_metadata,
      user_metadata: decoded.user_metadata,
    };
  } catch (error) {
    logger.error('Failed to extract user from token:', { error });
    return null;
  }
}

/**
 * Verify if a token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = jwt.decode(token) as SupabaseJWTPayload;
    
    if (!decoded || !decoded.exp) {
      return true;
    }
    
    // Check if token is expired (with 5 second buffer)
    return decoded.exp * 1000 < Date.now() - 5000;
  } catch (error) {
    return true;
  }
}

/**
 * Get token expiration time
 */
export function getTokenExpiration(token: string): Date | null {
  try {
    const decoded = jwt.decode(token) as SupabaseJWTPayload;
    
    if (!decoded || !decoded.exp) {
      return null;
    }
    
    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
}

/**
 * Custom error class for JWT-related errors
 */
export class SupabaseJWTError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SupabaseJWTError';
  }
}

/**
 * Extract bearer token from authorization header
 */
export function extractBearerToken(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Verify Supabase JWT token
 */
export async function verifySupabaseJWT(token: string): Promise<SupabaseUser> {
  try {
    logger.debug('Starting JWT verification', {
      tokenLength: token.length,
      tokenPrefix: token.substring(0, 20) + '...'
    });

    // First validate structure
    if (!validateTokenStructure(token)) {
      logger.warn('Token structure validation failed');
      throw new SupabaseJWTError('Invalid token structure');
    }

    // Check if token is expired
    if (isTokenExpired(token)) {
      logger.warn('Token is expired');
      throw new SupabaseJWTError('Token expired');
    }

    // Use Supabase client to verify the token
    const supabase = getSupabaseClient();

    try {
      logger.debug('Calling Supabase auth.getUser');
      // Set the auth session with the token
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        logger.error('Supabase auth.getUser failed:', {
          error: error?.message || 'No error message',
          errorCode: error?.status,
          hasUser: !!user
        });
        throw new SupabaseJWTError('Invalid token');
      }

      logger.debug('JWT verification successful', {
        userId: user.id,
        userEmail: user.email
      });

      // Convert to our user format
      return {
        id: user.id,
        email: user.email || '',
        name: user.user_metadata?.name,
        role: user.role,
        app_metadata: user.app_metadata as Record<string, unknown>,
        user_metadata: user.user_metadata as Record<string, unknown>,
      };
    } catch (authError) {
      logger.error('Supabase authentication failed:', {
        error: authError.message || 'Unknown auth error',
        errorType: authError.constructor.name
      });
      throw new SupabaseJWTError('Invalid token');
    }
  } catch (error) {
    if (error instanceof SupabaseJWTError) {
      logger.debug('JWT verification failed with SupabaseJWTError', { message: error.message });
      throw error;
    }
    logger.error('Unexpected error during JWT verification', {
      error: error.message || 'Unknown error',
      errorType: error.constructor.name
    });
    throw new SupabaseJWTError('Token verification failed');
  }
}

/**
 * Validate Supabase JWT structure (not signature or expiration)
 */
export function validateTokenStructure(token: string): boolean {
  try {
    const decoded = jwt.decode(token) as SupabaseJWTPayload;

    if (!decoded) {
      logger.debug('Token decode failed - token is null or invalid');
      return false;
    }

    // Check required fields (email is optional for some Supabase tokens)
    const requiredFields = ['sub', 'aud', 'exp', 'iat', 'iss'];
    for (const field of requiredFields) {
      if (!(field in decoded)) {
        logger.debug(`Token missing required field: ${field}`, {
          presentFields: Object.keys(decoded),
          tokenSub: decoded.sub,
          tokenAud: decoded.aud,
          tokenIss: decoded.iss
        });
        return false;
      }
    }

    // Note: We don't check expiration here - that's handled separately
    // This function only validates structure

    return true;
  } catch (error) {
    logger.debug('Token structure validation failed', { error: error.message });
    return false;
  }
}